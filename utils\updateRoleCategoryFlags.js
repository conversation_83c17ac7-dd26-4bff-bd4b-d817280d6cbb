const mongoose = require('mongoose');
const chalk = require('chalk');
const ServerConfig = require('../models/ServerConfig');
const databaseConnection = require('../config/database');

async function updateRoleCategoryFlags() {
    console.log(chalk.blue('🔄 Starting role category flags migration...'));
    
    try {
        // Connect to database
        await databaseConnection.connect();
        console.log(chalk.green('✅ Connected to database'));

        // Find all server configurations
        const serverConfigs = await ServerConfig.find({});
        console.log(chalk.blue(`📊 Found ${serverConfigs.length} server configurations`));

        let updatedCount = 0;

        for (const config of serverConfigs) {
            let needsUpdate = false;

            // Check if dungeonAlert exists and add missing role category flags
            if (config.dungeonAlert) {
                if (config.dungeonAlert.rankRolesEnabled === undefined) {
                    config.dungeonAlert.rankRolesEnabled = true;
                    needsUpdate = true;
                }
                if (config.dungeonAlert.worldRolesEnabled === undefined) {
                    config.dungeonAlert.worldRolesEnabled = true;
                    needsUpdate = true;
                }
                if (config.dungeonAlert.islandRolesEnabled === undefined) {
                    config.dungeonAlert.islandRolesEnabled = true;
                    needsUpdate = true;
                }
                if (config.dungeonAlert.specialRolesEnabled === undefined) {
                    config.dungeonAlert.specialRolesEnabled = true;
                    needsUpdate = true;
                }

                if (needsUpdate) {
                    await config.save();
                    updatedCount++;
                    console.log(chalk.green(`✅ Updated ${config.name} (${config.serverId})`));
                }
            }
        }

        console.log(chalk.green(`🎉 Migration completed! Updated ${updatedCount} server configurations`));

        // Verify the updates
        console.log(chalk.blue('\n🔍 Verifying updates...'));
        const verifyConfigs = await ServerConfig.find({ 'dungeonAlert.enabled': true });
        
        for (const config of verifyConfigs.slice(0, 3)) { // Show first 3 as examples
            console.log(chalk.blue(`📋 ${config.name}:`));
            console.log(chalk.blue(`  - Rank Roles Enabled: ${config.dungeonAlert.rankRolesEnabled}`));
            console.log(chalk.blue(`  - World Roles Enabled: ${config.dungeonAlert.worldRolesEnabled}`));
            console.log(chalk.blue(`  - Island Roles Enabled: ${config.dungeonAlert.islandRolesEnabled}`));
            console.log(chalk.blue(`  - Special Roles Enabled: ${config.dungeonAlert.specialRolesEnabled}`));
        }

    } catch (error) {
        console.error(chalk.red('❌ Migration failed:'), error);
        throw error;
    } finally {
        await mongoose.connection.close();
        console.log(chalk.blue('🔌 Database connection closed'));
    }
}

// Run the migration if this file is executed directly
if (require.main === module) {
    updateRoleCategoryFlags()
        .then(() => {
            console.log(chalk.green('✅ Role category flags migration completed successfully'));
            process.exit(0);
        })
        .catch((error) => {
            console.error(chalk.red('❌ Migration failed:'), error);
            process.exit(1);
        });
}

module.exports = updateRoleCategoryFlags;