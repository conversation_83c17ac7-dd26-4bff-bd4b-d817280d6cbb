const {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uilder,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    ChannelType,
    PermissionFlagsBits
} = require('discord.js');
const configService = require('../../../services/configService');
const chalk = require('chalk');

// Setup Emojis - Centralized emoji definitions for the setup system
const SETUP_EMOJIS = {
    // Status emojis
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    ENABLED: '<:enabled_RB:1384533989444030655>',
    DISABLED: '<:disabled_RB:1384534072675664002>',
    VALID: '✅',
    INVALID: '❌',

    // Feature emojis
    DUNGEONS: '⁠🌀',
    WORLD_BOSS: '👹',
    CHANNELS: '📢',
    ROLES: '🎭',
    GAME: '🎮',
    COMING_SOON: '🚧',

    // Role type emojis
    RANK_ROLES: '🏆',
    WORLD_ROLES: '🌍',
    ISLAND_ROLES: '🏝️',
    SPECIAL_ROLES: '⭐',

    // Action emojis
    CREATE_NEW: '🆕',
    USE_EXISTING: '🎭',
    ENABLE: '1384575597656674446',
    DISABLE: '1384575688039727244',
    BACK: '🔙',
    CANCEL: '❌',

    // Process emojis
    LOADING: '⏳',
    PROGRESS: '🔄',
    PROGRESS_BAR: '📊',

    // Management emojis
    MANAGE: '🔧',
    DELETE: '🗑️',
    CLEANUP: '🧹',
    SETUP: '🧱',
    TARGET: '🎯',
    LOCK: '🔒',
    LABEL: '🏷️',

    // Special action emojis
    FINISH: '✅',
    CONFIRM: '✅',
    KEEP: '🔒',

    // Additional emojis for comprehensive coverage
    OVERWRITE: '⚠️',
    TIMEOUT: '⏰',
    MAPPED: '✅',
    NOT_SET: '❌',
    CONFIGURED: '✅',
    NOT_CONFIGURED: '❌',
    REMOVE: '🗑️',
    CLEAN: '🧹',
    ALL_SUCCESS: '✅',
    FAILED: '❌',
    DELETED: '🗑️',
    KEPT: '🔧',
    SUMMARY: '📊',

    // Additional missing emojis found in the code
    ASSIGN: '🎭',
    VALIDATE: '✅',
    INVALID_ROLE: '❌',
    ROLE_CREATED: '✅',
    ROLE_ASSIGNED: '✅',
    ROLE_REMOVED: '🗑️',
    MORE_SETUP: '🧱',

    // Additional emojis found throughout the file
    MAP: '🎯',
    CONFIRM_MAPPING: '✅',
    ROLE_MANAGEMENT: '🔧',
    VALID_ROLES: '✅',
    INVALID_ROLES: '❌',
    REMOVE_ROLES: '🗑️',
    CLEAN_INVALID: '🧹',
    REMOVE_ALL: '🗑️',
    CONFIRM_REMOVAL: '⚠️',
    REMOVE_CONFIG_ONLY: '🔧',
    REMOVE_AND_DELETE: '🗑️',
    CANCEL: '🔙',
    PROCESSING: '⏳',
    COMPLETE: '✅',
    CLEANUP_COMPLETE: '✅',
    MANAGE_ROLES: '🔧',
    CHANNEL_SELECTED: '✅',
    SETUP_PING_ROLES: '🧱',
    BACK_TO_MAIN: '🔙',
    FINISH_SETUP: '✅',
    ROLE_INPUT: '🎯',
    TIMEOUT_WARNING: '⏰',
    INVALID_FORMAT: '❌',
    ROLE_NOT_FOUND: '❌',
    CHANNEL_NOT_FOUND: '❌',
    INVALID_CHANNEL_TYPE: '❌',
    DISABLE_WARNING: '⚠️',
    KEEP_ROLES: '🔒',
    DELETE_ROLES: '🗑️',
    DISABLED: '✅',
    DELETED_COUNT: '🗑️',
    FAILED_COUNT: '❌',
    ALL_SUCCESS_DELETE: '✅',
    DISABLED_CONFIG: '🔒'
};

// Session management for setup process
const setupSessions = new Map();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes in milliseconds


module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Interactive setup for Arise Crossover features')
        .addSubcommand(subcommand =>
            subcommand
                .setName('arise_crossover')
                .setDescription('Set up Arise Crossover game features')
                .addStringOption(option =>
                    option.setName('feature')
                        .setDescription('Directly access a specific feature setup')
                        .addChoices(
                            { name: 'Auto Dungeons', value: 'auto_dungeons' },
                            { name: 'Auto World Boss (Coming Soon)', value: 'auto_worldboss' }
                        )
                        .setRequired(false)))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),
    name: 'setup',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: '/setup',
    description: 'Interactive setup for Arise Crossover features',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(interaction) {
        try {
            // Check permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} You need the "Manage Server" permission to use this command.`,
                    ephemeral: true
                });
            }

            const subcommand = interaction.options.getSubcommand();
            
            if (subcommand === 'arise_crossover') {
                const directFeature = interaction.options.getString('feature');
                
                if (directFeature) {
                    // Direct feature access
                    return await this.handleDirectFeatureAccess(interaction, directFeature);
                } else {
                    // Show main feature selection
                    return await this.showFeatureSelection(interaction);
                }
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error in setup command:`), error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle(`${SETUP_EMOJIS.ERROR} Setup Error`)
                .setDescription('An error occurred during setup. Please try again or contact support.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed], components: [] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async showFeatureSelection(interaction) {
        // Check for existing session
        const existingSession = setupSessions.get(interaction.user.id);
        if (existingSession) {
            return await interaction.reply({
                content: `${SETUP_EMOJIS.WARNING} You already have an active setup session. Please complete or cancel it first.`,
                ephemeral: true
            });
        }

        // Check current configuration
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.GAME} Arise Crossover Setup`)
            .setDescription('Welcome to the interactive setup for Arise Crossover features!\n\nSelect a feature to configure:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.DUNGEONS} Auto Dungeons`,
                    value: currentConfig?.dungeonAlert?.enabled 
                        ? `${SETUP_EMOJIS.ENABLED} Currently **Enabled**` 
                        : `${SETUP_EMOJIS.DISABLED} Currently **Disabled**`,
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.WORLD_BOSS} Auto World Boss`,
                    value: currentConfig?.worldBossAlert?.enabled 
                        ? `${SETUP_EMOJIS.ENABLED} Currently **Enabled**` 
                        : `${SETUP_EMOJIS.COMING_SOON} **Coming Soon**`,
                    inline: true
                }
            ])
            .setFooter({ text: 'Session will timeout in 10 minutes' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_auto_dungeons')
                    .setLabel(`${SETUP_EMOJIS.DUNGEONS} Auto Dungeons`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_auto_worldboss')
                    .setLabel(`${SETUP_EMOJIS.WORLD_BOSS} Auto World Boss`)
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true), // Coming soon
                new ButtonBuilder()
                    .setCustomId('setup_cancel')
                    .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                    .setStyle(ButtonStyle.Danger)
            );

        await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });

        // Create session
        this.createSession(interaction.user.id, {
            step: 'feature_selection',
            guildId: interaction.guild.id,
            channelId: interaction.channel.id,
            startTime: Date.now()
        });
    },

    async handleDirectFeatureAccess(interaction, feature) {
        if (feature === 'auto_dungeons') {
            return await this.showAutoDungeonsSetup(interaction);
        } else if (feature === 'auto_worldboss') {
            return await interaction.reply({
                content: `${SETUP_EMOJIS.COMING_SOON} Auto World Boss feature is coming soon! Please check back later.`,
                ephemeral: true
            });
        }
    },

    async showAutoDungeonsSetup(interaction) {
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const dungeonConfig = currentConfig?.dungeonAlert;

        // Check session for selected channel (not yet saved to database)
        const session = this.getSession(interaction.user.id);
        const selectedChannelId = session?.selectedChannelId;

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.DUNGEONS} Auto Dungeons Setup`)
            .setDescription('Configure automatic dungeon alerts for your server.')
            .addFields([
                {
                    name: 'Current Status',
                    value: dungeonConfig?.enabled ? `${SETUP_EMOJIS.ENABLED} Enabled` : `${SETUP_EMOJIS.DISABLED} Disabled`,
                    inline: true
                },
                {
                    name: 'Target Channel',
                    value: selectedChannelId
                        ? `<#${selectedChannelId}> (Selected)`
                        : dungeonConfig?.targetChannelId
                            ? `<#${dungeonConfig.targetChannelId}>`
                            : 'Not configured',
                    inline: true
                }
            ])
            .setFooter({ text: 'Choose an option below to continue' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_select_channel')
                    .setLabel(`${SETUP_EMOJIS.CHANNELS} Select Target Channel`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel(`${SETUP_EMOJIS.ROLES} Setup Ping Roles`)
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_toggle_enable')
                    .setLabel(dungeonConfig?.enabled ? `Disable Auto Dungeons` : `Enable Auto Dungeons`)
                    .setEmoji(dungeonConfig?.enabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(dungeonConfig?.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row1, row2] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row1, row2], ephemeral: true });
        }

        // Update or create session
        this.updateSession(interaction.user.id, {
            step: 'auto_dungeons_setup',
            guildId: interaction.guild.id,
            feature: 'auto_dungeons'
        });
    },

    createSession(userId, sessionData) {
        setupSessions.set(userId, sessionData);
        
        // Auto-cleanup session after timeout
        setTimeout(() => {
            setupSessions.delete(userId);
        }, SESSION_TIMEOUT);
    },

    updateSession(userId, updates) {
        const session = setupSessions.get(userId);
        if (session) {
            Object.assign(session, updates);
        } else {
            this.createSession(userId, updates);
        }
    },

    getSession(userId) {
        return setupSessions.get(userId);
    },

    clearSession(userId) {
        setupSessions.delete(userId);
    },

    // Helper method to get role color based on role type
    getRoleColor(rank) {
        switch (rank) {
            case 'E': return '#8897aa';
            case 'D': return '#4488ff';
            case 'C': return '#44aaff';
            case 'B': return '#6644ff';
            case 'A': return '#8844ff';
            case 'S': return '#aa44ff';
            case 'SS': return '#ff44ff';
            case 'G': return '#660066'; // Dark purple for G rank
            case 'N': return '#9900cc'; // Brighter purple for N rank
            case 'DUNGEON_PING': return '#00ff00';
            case 'RED_DUNGEON': return '#ff0000';
            case 'DOUBLE_DUNGEON': return '#ffaa00';
            default: return '#ffffff';
        }
    },

    // Handle button interactions
    async handleButtonInteraction(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} No active setup session found. Please start a new setup.`,
                    ephemeral: true
                });
            }

            const customId = interaction.customId;

            switch (customId) {
                case 'setup_auto_dungeons':
                    await this.showAutoDungeonsSetup(interaction);
                    break;

                case 'setup_cancel':
                    await this.handleCancel(interaction);
                    break;

                case 'setup_back':
                    await this.handleBackNavigation(interaction);
                    break;

                case 'dungeons_select_channel':
                    await this.showChannelSelection(interaction);
                    break;

                case 'dungeons_setup_roles':
                    await this.showRoleSelectionOptions(interaction);
                    break;

                case 'dungeons_toggle_enable':
                    await this.toggleAutoDungeons(interaction);
                    break;

                case 'dungeons_use_existing_channel':
                    await this.showExistingChannels(interaction);
                    break;

                case 'dungeons_create_new_channel':
                    await this.createNewChannel(interaction);
                    break;

                case 'dungeons_use_existing_roles':
                    await this.showRoleSelectionOptions(interaction);
                    break;

                case 'dungeons_create_new_roles':
                case 'dungeons_create_all_roles':
                    await this.createNewRoles(interaction);
                    break;

                case 'setup_rank_roles':
                    await this.setupRankRoles(interaction);
                    break;

                case 'setup_world_roles':
                    await this.setupWorldRoles(interaction);
                    break;

                case 'setup_island_roles':
                    await this.setupIslandRoles(interaction);
                    break;

                case 'setup_special_roles':
                    await this.setupSpecialRoles(interaction);
                    break;

                // Role creation handlers
                case 'create_rank_roles':
                case 'create_world_roles':
                case 'create_island_roles':
                case 'create_special_roles':
                    await this.createSpecificRoles(interaction, customId);
                    break;

                // Existing role selection handlers
                case 'select_existing_rank_roles':
                    await this.showExistingRoleSelection(interaction, 'rank');
                    break;

                case 'select_existing_world_roles':
                    await this.showExistingRoleSelection(interaction, 'world');
                    break;

                case 'select_existing_island_roles':
                    await this.showExistingRoleSelection(interaction, 'island');
                    break;

                case 'select_existing_special_roles':
                    await this.showExistingRoleSelection(interaction, 'special');
                    break;

                // Role management handlers
                case 'manage_existing_roles':
                    await this.showRoleManagement(interaction);
                    break;

                case 'unset_roles':
                    await this.showRoleUnsetting(interaction);
                    break;

                case 'cleanup_invalid_roles':
                    await this.cleanupInvalidRoles(interaction);
                    break;

                case 'unset_all_roles':
                    await this.handleRoleUnsetting(interaction);
                    break;

                case 'confirm_remove_config_only':
                case 'confirm_remove_and_delete':
                    await this.handleRoleDeletion(interaction);
                    break;

                case 'confirm_role_mapping':
                    await this.confirmRoleMapping(interaction);
                    break;

                // Role category toggle handlers
                case 'toggle_rank_roles':
                    await this.toggleRoleCategory(interaction, 'rank');
                    break;

                case 'toggle_world_roles':
                    await this.toggleRoleCategory(interaction, 'world');
                    break;

                case 'toggle_island_roles':
                    await this.toggleRoleCategory(interaction, 'island');
                    break;

                case 'toggle_special_roles':
                    await this.toggleRoleCategory(interaction, 'special');
                    break;



                default:
                    if (customId === 'select_role_to_assign') {
                        await this.handleRoleAssignmentSelection(interaction);
                    } else if (customId === 'select_existing_channel') {
                        await this.handleChannelSelection(interaction);
                    } else if (customId.startsWith('disable_') && customId.endsWith('_keep')) {
                        await this.handleDisableRoleKeep(interaction);
                    } else if (customId.startsWith('disable_') && customId.endsWith('_delete')) {
                        await this.handleDisableRoleDelete(interaction);
                    } else if (customId.startsWith('select_role_')) {
                        await this.handleRoleSelection(interaction);
                    } else if (customId.startsWith('map_role_')) {
                        await this.handleRoleMapping(interaction);
                    } else if (customId.startsWith('unset_role_') || customId === 'select_roles_to_unset') {
                        await this.handleRoleUnsetting(interaction);
                    } else if (customId.startsWith('delete_role_')) {
                        await this.handleRoleDeletion(interaction);
                    }
                    break;
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling button interaction:`), error);

            const errorMessage = this.getDetailedErrorMessage(error);

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage,
                    embeds: [],
                    components: []
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral: true
                });
            }
        }
    },

    async handleCancel(interaction) {
        this.clearSession(interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle(`${SETUP_EMOJIS.CANCEL} Setup Cancelled`)
            .setDescription('Setup has been cancelled. You can start a new setup anytime using `/setup arise_crossover`.')
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [] });
    },

    async handleBackNavigation(interaction) {
        const session = this.getSession(interaction.user.id);
        if (!session) {
            return await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} No active setup session found. Please start a new setup.`,
                ephemeral: true
            });
        }

        // Navigate back based on current step
        switch (session.step) {
            case 'auto_dungeons_setup':
                await this.showFeatureSelection(interaction);
                break;
            case 'channel_selection':
                await this.showAutoDungeonsSetup(interaction);
                break;
            case 'role_selection_options':
                await this.showAutoDungeonsSetup(interaction);
                break;
            case 'rank_roles_setup':
            case 'world_roles_setup':
            case 'island_roles_setup':
            case 'special_roles_setup':
                await this.showRoleSelectionOptions(interaction);
                break;
            case 'rank_role_assignment':
            case 'world_role_assignment':
            case 'island_role_assignment':
            case 'special_role_assignment':
                // Go back to the specific role setup
                const roleType = session.currentRoleType;
                if (roleType === 'rank') {
                    await this.setupRankRoles(interaction);
                } else if (roleType === 'world') {
                    await this.setupWorldRoles(interaction);
                } else if (roleType === 'island') {
                    await this.setupIslandRoles(interaction);
                } else if (roleType === 'special') {
                    await this.setupSpecialRoles(interaction);
                } else {
                    await this.showRoleSelectionOptions(interaction);
                }
                break;
            case 'role_management':
            case 'role_unsetting':
            case 'role_removal_confirmation':
                await this.showRoleSelectionOptions(interaction);
                break;
            case 'channel_selected':
                await this.showAutoDungeonsSetup(interaction);
                break;
            default:
                await this.showFeatureSelection(interaction);
                break;
        }
    },

    getDetailedErrorMessage(error) {
        if (error.code === 50013) {
            return `${SETUP_EMOJIS.ERROR} I don\'t have the required permissions. Please ensure I have "Manage Roles" and "Manage Channels" permissions.`;
        } else if (error.code === 50001) {
            return `${SETUP_EMOJIS.ERROR} Missing access to the specified channel or role. Please check permissions.`;
        } else if (error.code === 10011) {
            return `${SETUP_EMOJIS.ERROR} The specified role was not found. It may have been deleted.`;
        } else if (error.code === 10003) {
            return `${SETUP_EMOJIS.ERROR} The specified channel was not found. It may have been deleted.`;
        } else if (error.message?.includes('timeout')) {
            return `${SETUP_EMOJIS.ERROR} The operation timed out. Please try again.`;
        } else {
            return `${SETUP_EMOJIS.ERROR} An unexpected error occurred. Please try again or contact support if the issue persists.`;
        }
    },

    async showChannelSelection(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentChannelId = config?.dungeonAlert?.targetChannelId;
        
        // Check session for selected channel (not yet saved to database)
        const session = this.getSession(interaction.user.id);
        const selectedChannelId = session?.selectedChannelId;

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.CHANNELS} Channel Selection`)
            .setDescription('Choose how you want to set up the target channel for dungeon alerts:')
            .addFields([
                {
                    name: 'Current Configuration',
                    value: selectedChannelId
                        ? `<#${selectedChannelId}> (Selected - not saved yet)`
                        : currentChannelId
                            ? `<#${currentChannelId}> (Currently configured)`
                            : `${SETUP_EMOJIS.ERROR} Not configured`,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Use Existing**: Select from your server\'s existing text channels\n• **Create New**: Create a new #dungeons channel automatically',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_channel')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing Channel`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_channel')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New Channel`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'channel_selection' });
    },

    async toggleAutoDungeons(interaction) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    },
                    worldBossAlert: { enabled: false },
                    infernalAlert: { enabled: false }
                };
            }

            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            // Toggle the enabled state
            const newState = !config.dungeonAlert.enabled;
            config.dungeonAlert.enabled = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show updated setup
            await this.showAutoDungeonsSetup(interaction);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling auto dungeons:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle auto dungeons. Please try again.`,
                ephemeral: true
            });
        }
    },

    async showExistingChannels(interaction) {
        // Get current configuration
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentChannelId = config?.dungeonAlert?.targetChannelId;
        
        // Get available text channels
        const textChannels = interaction.guild.channels.cache
            .filter(channel => channel.type === ChannelType.GuildText)
            .sort((a, b) => a.name.localeCompare(b.name))
            .first(25); // Discord select menu limit

        if (textChannels.length === 0) {
            return await interaction.update({
                content: `${SETUP_EMOJIS.ERROR} No text channels found in this server. Please create a channel first or use the "Create New Channel" option.`,
                embeds: [],
                components: [new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                            .setStyle(ButtonStyle.Secondary)
                    )]
            });
        }

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.LABEL} Select Existing Channel`)
            .setDescription('Choose a text channel from the dropdown below:')
            .addFields([
                {
                    name: 'Current Configuration',
                    value: currentChannelId && interaction.guild.channels.cache.has(currentChannelId)
                        ? `<#${currentChannelId}> (Currently configured)`
                        : `${SETUP_EMOJIS.NOT_CONFIGURED} Not configured`,
                    inline: false
                },
                {
                    name: 'Instructions',
                    value: 'Select a channel from the dropdown menu below. The channel will be immediately saved to the configuration.',
                    inline: false
                }
            ])
            .setTimestamp();

        // Create dropdown with channel options
        const options = textChannels.map(channel => ({
            label: `#${channel.name}`,
            value: channel.id,
            description: `${channel.members.size} members can see this channel`,
            emoji: currentChannelId === channel.id ? SETUP_EMOJIS.SUCCESS : SETUP_EMOJIS.CHANNELS
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_existing_channel')
            .setPlaceholder('Choose a channel...')
            .addOptions(options);

        const row1 = new ActionRowBuilder().addComponents(selectMenu);
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            components: [row1, row2]
        });

        this.updateSession(interaction.user.id, { step: 'existing_channel_selection' });
    },

    async createNewChannel(interaction) {
        try {
            // Check if bot has permission to create channels
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create channels. Please give me the "Manage Channels" permission or use an existing channel.`,
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating channel... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Create the dungeons channel
            const channel = await interaction.guild.channels.create({
                name: 'dungeons',
                type: ChannelType.GuildText,
                topic: 'Automatic dungeon alerts from Arise Crossover',
                reason: 'Created by setup command for dungeon alerts'
            });

            // Immediately save to MongoDB
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        targetChannelId: channel.id,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    }
                };
            } else {
                if (!config.dungeonAlert) {
                    config.dungeonAlert = {
                        enabled: false,
                        targetChannelId: channel.id,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    };
                } else {
                    config.dungeonAlert.targetChannelId = channel.id;
                }
            }
            await configService.saveServerConfig(interaction.guild.id, config);

            // Update session with selected channel
            this.updateSession(interaction.user.id, {
                selectedChannelId: channel.id,
                step: 'channel_selected'
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} Channel Created & Configured`)
                .setDescription(`Successfully created ${channel} for dungeon alerts!`)
                .addFields([
                    {
                        name: 'Configuration Status',
                        value: `${SETUP_EMOJIS.SUCCESS} Channel has been automatically saved to the database`,
                        inline: false
                    },
                    {
                        name: 'Next Steps',
                        value: 'You can now set up ping roles or go back to the main setup.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.SETUP} Setup Ping Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to Main Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating channel:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create channel. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },



    async showRoleSelectionOptions(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.USE_EXISTING} Role Selection Options`)
            .setDescription('Choose which types of roles you want to set up:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.RANK_ROLES} Rank Roles (E-SS)`,
                    value: 'Roles for different dungeon ranks',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.WORLD_ROLES} World Roles`,
                    value: 'World 1 and World 2 ping roles',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.ISLAND_ROLES} Island Roles`,
                    value: 'Individual island ping roles',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.SPECIAL_ROLES} Special Roles`,
                    value: 'Red Gate, Double Dungeon, General Ping',
                    inline: true
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_rank_roles')
                    .setLabel(`${SETUP_EMOJIS.RANK_ROLES} Rank Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_world_roles')
                    .setLabel(`${SETUP_EMOJIS.WORLD_ROLES} World Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_island_roles')
                    .setLabel(`${SETUP_EMOJIS.ISLAND_ROLES} Island Roles`)
                    .setStyle(ButtonStyle.Primary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_special_roles')
                    .setLabel(`${SETUP_EMOJIS.SPECIAL_ROLES} Special Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_all_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create All Roles`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'role_selection_options' });
    },

    async showRoleSetup(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.SETUP} Role Setup`)
            .setDescription('Choose how you want to set up ping roles for dungeon alerts:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.USE_EXISTING} Use Existing Roles`,
                    value: 'Select from your server\'s existing roles',
                    inline: false
                },
                {
                    name: `${SETUP_EMOJIS.CREATE_NEW} Create New Roles`,
                    value: 'Automatically create all necessary roles with proper colors',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New Roles`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'role_setup' });
    },

    async createNewRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create roles. Please give me the "Manage Roles" permission or use existing roles.`,
                    embeds: [],
                    components: []
                });
            }

            // Check for existing roles in the config
            const config = await configService.getServerConfig(interaction.guild.id);
            const hasExistingRoles = config && config.dungeonAlert && (
                Object.keys(config.dungeonAlert.dungeonRoles || {}).length > 0 ||
                Object.keys(config.dungeonAlert.worldRoles || {}).length > 0 ||
                Object.keys(config.dungeonAlert.islandRoles || {}).length > 0
            );
            if (hasExistingRoles && !interaction.customId?.endsWith('_confirm_overwrite')) {
                // Show warning and require confirmation
                const warningEmbed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.OVERWRITE} Overwrite Existing Roles?`)
                    .setDescription('This will **delete all currently configured roles** in the database and create new ones.\n\nAre you sure you want to continue?')
                    .addFields([
                        { name: 'Current Configured Roles', value: [
                            ...Object.values(config.dungeonAlert.dungeonRoles || {}).map(id => `<@&${id}>`),
                            ...Object.values(config.dungeonAlert.worldRoles || {}).map(id => `<@&${id}>`),
                            ...Object.values(config.dungeonAlert.islandRoles || {}).map(id => `<@&${id}>`)
                        ].join(', ') || 'None', inline: false }
                    ])
                    .setFooter({ text: 'This action cannot be undone.' })
                    .setTimestamp();
                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('dungeons_create_all_roles_confirm_overwrite')
                            .setLabel(`${SETUP_EMOJIS.OVERWRITE} Overwrite & Create New Roles`)
                            .setStyle(ButtonStyle.Danger),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Cancel`)
                            .setStyle(ButtonStyle.Secondary)
                    );
                await interaction.update({ embeds: [warningEmbed], components: [row] });
                return;
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating roles... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Get shared config for island names
            const configWrapper = require('../../../services/configWrapper');
            const sharedConfig = await configWrapper.getSharedConfig();
            const allIslands = [
                ...(sharedConfig?.worldIslands?.[1] || []),
                ...(sharedConfig?.worldIslands?.[2] || [])
            ];

            // Define role names
            const dungeonRoleNames = {
                E: 'E Dungeon Ping',
                D: 'D Dungeon Ping',
                C: 'C Dungeon Ping',
                B: 'B Dungeon Ping',
                A: 'A Dungeon Ping',
                S: 'S Dungeon Ping',
                SS: 'SS Dungeon Ping',
                G: 'G Dungeon Ping',
                N: 'N Dungeon Ping',
                DUNGEON_PING: 'Dungeons Ping',
                RED_DUNGEON: 'Red Gate Ping',
                DOUBLE_DUNGEON: 'Double Dungeon Ping'
            };

            const worldRoleNames = {
                1: 'World 1 Ping',
                2: 'World 2 Ping'
            };

            const islandRoleNames = {
                'Leveling City': 'Leveling City Ping',
                'Grass Village': 'Grass Village Ping',
                'Brum Island': 'Brum Island Ping',
                'Faceheal Town': 'Faceheal Town Ping',
                'Lucky Kingdom': 'Lucky Kingdom Ping',
                'Nipon City': 'Nipon City Ping',
                'Mori Town': 'Mori Town Ping',
                'Dragon City': 'Dragon City Ping',
                'XZ City': 'XZ City Ping',
                'Kindama City': 'Kindama City Ping',
                'Hunters City': 'Hunters City Ping',
                'Nen City': 'Nen City Ping',
                'Hurricane Town': 'Hurricane Town Ping'
            };

            const roleData = [
                // Rank roles
                { name: dungeonRoleNames.E, key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                { name: dungeonRoleNames.D, key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                { name: dungeonRoleNames.C, key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                { name: dungeonRoleNames.B, key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                { name: dungeonRoleNames.A, key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                { name: dungeonRoleNames.S, key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                { name: dungeonRoleNames.SS, key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                { name: dungeonRoleNames.G, key: 'G', type: 'dungeon', color: this.getRoleColor('G') },
                { name: dungeonRoleNames.N, key: 'N', type: 'dungeon', color: this.getRoleColor('N') },
                // Special roles
                { name: dungeonRoleNames.DUNGEON_PING, key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                { name: dungeonRoleNames.RED_DUNGEON, key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                { name: dungeonRoleNames.DOUBLE_DUNGEON, key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') },
                // World roles
                { name: worldRoleNames[1], key: '1', type: 'world', color: '#00ff00' },
                { name: worldRoleNames[2], key: '2', type: 'world', color: '#ff0000' }
            ];

            // Add island roles
            allIslands.forEach(island => {
                roleData.push({
                    name: islandRoleNames[island] || `${island} Ping`,
                    key: island,
                    type: 'island',
                    color: '#3498db' // Blue color for island roles
                });
            });

            // Get session for channel info
            const session = this.getSession(interaction.user.id);

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Creating All Roles`)
                .setDescription('Please wait while I create all the necessary roles...')
                .addFields([
                    { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '⚙️ Status', value: `🔄 Creating roles... (0/${roleData.length})`, inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            const createdRoles = {};
            const dungeonRoles = {};
            const worldRoles = {};
            const islandRoles = {};
            const totalRoles = roleData.length;
            let roleCount = 0;

            for (const roleInfo of roleData) {
                try {
                    roleCount++;

                    // Update progress
                    progressEmbed.setFields(
                        { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                        { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})\nCurrent: **${roleInfo.name}**`, inline: true }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });

                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: 'Created by setup command for dungeon alerts'
                    });

                    createdRoles[roleInfo.key] = role.id;

                    if (roleInfo.type === 'world') {
                        worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        islandRoles[roleInfo.key] = role.id;
                    } else {
                        dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update progress to saving configuration
            progressEmbed.setFields(
                { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
            );
            await interaction.editReply({ embeds: [progressEmbed] });

            // Save configuration to MongoDB
            let newConfig = config;
            if (!newConfig) {
                newConfig = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: { enabled: false, dungeonRoles: {}, worldRoles: {}, islandRoles: {} }
                };
            }
            if (!newConfig.dungeonAlert) newConfig.dungeonAlert = { enabled: false, dungeonRoles: {}, worldRoles: {}, islandRoles: {} };
            newConfig.dungeonAlert.dungeonRoles = dungeonRoles;
            newConfig.dungeonAlert.worldRoles = worldRoles;
            newConfig.dungeonAlert.islandRoles = islandRoles;
            await configService.saveServerConfig(interaction.guild.id, newConfig);

            // Update session with created roles
            this.updateSession(interaction.user.id, {
                dungeonRoles,
                worldRoles,
                islandRoles,
                step: 'roles_created'
            });

            // Create success embed
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Auto Dungeons Setup Complete!')
                .setDescription('Dungeon alert system has been successfully configured for this server.\n\n**🚀 Configuration is now active!** The server will immediately start receiving dungeon alerts without requiring a bot restart.')
                .setColor('#2ecc71')
                .addFields(
                    { name: '📍 Alert Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '🎭 Roles Created', value: `${Object.keys(createdRoles).length} roles`, inline: true },
                    { name: '🔔 Status', value: 'Active & Ready', inline: true },
                    {
                        name: 'Rank Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'Special Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'World & Island Roles',
                        value: [
                            ...Object.values(worldRoles).map(roleId => `<@&${roleId}>`),
                            ...Object.values(islandRoles).slice(0, 5).map(roleId => `<@&${roleId}>`)
                        ].join(', ') + (Object.keys(islandRoles).length > 5 ? ` +${Object.keys(islandRoles).length - 5} more` : ''),
                        inline: false
                    }
                )
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [successEmbed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    
    async setupRankRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.dungeonRoles || {};
        const isEnabled = config?.dungeonAlert?.rankRolesEnabled !== false; // Default to true

        const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
        const roleStatus = rankKeys.map(key => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**${key} Rank**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.ERROR} Not Set`}`;
        }).join('\n');

        const configuredCount = rankKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.RANK_ROLES} Rank Roles Setup`)
            .setDescription('Choose how to set up rank roles (E, D, C, B, A, S, SS):')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${rankKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all rank roles with proper colors\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_rank_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_rank_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_rank_roles')
                    .setLabel(isEnabled ? `Disable Rank Roles` : `Enable Rank Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'rank_roles_setup',
            currentRoleType: 'rank'
        });
    },

    async setupWorldRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.worldRoles || {};
        const isEnabled = config?.dungeonAlert?.worldRolesEnabled !== false; // Default to true

        const worldKeys = ['1', '2'];
        const roleStatus = worldKeys.map(key => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**World ${key} Ping**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.NOT_SET} Not Set`}`;
        }).join('\n');

        const configuredCount = worldKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.WORLD_ROLES} World Roles Setup`)
            .setDescription('Choose how to set up world ping roles (World 1, World 2):')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${worldKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create world ping roles\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_world_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_world_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_world_roles')
                    .setLabel(isEnabled ? `Disable World Roles` : `Enable World Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'world_roles_setup',
            currentRoleType: 'world'
        });
    },

    async setupIslandRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.islandRoles || {};
        const isEnabled = config?.dungeonAlert?.islandRolesEnabled !== false; // Default to true

        const islandKeys = ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town',
                           'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City',
                           'XZ City', 'Kindama City', 'Hunters City', 'Nen City', 'Hurricane Town'];

        const configuredCount = islandKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.ISLAND_ROLES} Island Roles Setup`)
            .setDescription('Choose how to set up individual island ping roles:')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${islandKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: islandKeys.slice(0, 10).map(key => {
                        const roleId = currentRoles[key];
                        const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
                        return `**${key}**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.NOT_SET} Not Set`}`;
                    }).join('\n') + (islandKeys.length > 10 ? `\n*...and ${islandKeys.length - 10} more islands*` : ''),
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create roles for all islands\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_island_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_island_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_island_roles')
                    .setLabel(isEnabled ? `Disable Island Roles` : `Enable Island Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'island_roles_setup',
            currentRoleType: 'island'
        });
    },

    async setupSpecialRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.dungeonRoles || {};
        const isEnabled = config?.dungeonAlert?.specialRolesEnabled !== false; // Default to true

        const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
        const specialNames = {
            'DUNGEON_PING': 'General Dungeon Ping',
            'RED_DUNGEON': 'Red Gate Ping',
            'DOUBLE_DUNGEON': 'Double Dungeon Ping'
        };

        const roleStatus = specialKeys.map(key => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**${specialNames[key]}**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.NOT_SET} Not Set`}`;
        }).join('\n');

        const configuredCount = specialKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.SPECIAL_ROLES} Special Roles Setup`)
            .setDescription('Choose how to set up special ping roles:')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${specialKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Special Roles Include',
                    value: '• **Dungeon Ping**: General dungeon notifications\n• **Red Gate**: Red dungeon alerts\n• **Double Dungeon**: Double dungeon alerts',
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all special roles\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_special_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_special_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_special_roles')
                    .setLabel(isEnabled ? `Disable Special Roles` : `Enable Special Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'special_roles_setup',
            currentRoleType: 'special'
        });
    },

    async createSpecificRoles(interaction, roleType) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create roles. Please give me the "Manage Roles" permission.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    }
                };
            }
            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            const session = this.getSession(interaction.user.id);
            const currentRoles = {
                dungeonRoles: { ...config.dungeonAlert.dungeonRoles },
                worldRoles: { ...config.dungeonAlert.worldRoles },
                islandRoles: { ...config.dungeonAlert.islandRoles }
            };

            let rolesToCreate = [];
            let roleCategory = '';

            switch (roleType) {
                case 'create_rank_roles':
                    roleCategory = 'Rank Roles';
                    rolesToCreate = [
                        { name: 'E Dungeon Ping', key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                        { name: 'D Dungeon Ping', key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                        { name: 'C Dungeon Ping', key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                        { name: 'B Dungeon Ping', key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                        { name: 'A Dungeon Ping', key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                        { name: 'S Dungeon Ping', key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                        { name: 'SS Dungeon Ping', key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                        { name: 'G Dungeon Ping', key: 'G', type: 'dungeon', color: this.getRoleColor('G') },
                        { name: 'N Dungeon Ping', key: 'N', type: 'dungeon', color: this.getRoleColor('N') }
                    ];
                    break;

                case 'create_world_roles':
                    roleCategory = 'World Roles';
                    rolesToCreate = [
                        { name: 'World 1 Ping', key: '1', type: 'world', color: '#00ff00' },
                        { name: 'World 2 Ping', key: '2', type: 'world', color: '#ff0000' }
                    ];
                    break;

                case 'create_special_roles':
                    roleCategory = 'Special Roles';
                    rolesToCreate = [
                        { name: 'Dungeons Ping', key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                        { name: 'Red Gate Ping', key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                        { name: 'Double Dungeon Ping', key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') }
                    ];
                    break;

                case 'create_island_roles':
                    roleCategory = 'Island Roles';
                    const configWrapper = require('../../../services/configWrapper');
                    const sharedConfig = await configWrapper.getSharedConfig();
                    const allIslands = [
                        ...(sharedConfig?.worldIslands?.[1] || []),
                        ...(sharedConfig?.worldIslands?.[2] || [])
                    ];

                    const islandRoleNames = {
                        'Leveling City': 'Leveling City Ping',
                        'Grass Village': 'Grass Village Ping',
                        'Brum Island': 'Brum Island Ping',
                        'Faceheal Town': 'Faceheal Town Ping',
                        'Lucky Kingdom': 'Lucky Kingdom Ping',
                        'Nipon City': 'Nipon City Ping',
                        'Mori Town': 'Mori Town Ping',
                        'Dragon City': 'Dragon City Ping',
                        'XZ City': 'XZ City Ping',
                        'Kindama City': 'Kindama City Ping',
                        'Hunters City': 'Hunters City Ping',
                        'Nen City': 'Nen City Ping',
                        'Hurricane Town': 'Hurricane Town Ping'
                    };

                    rolesToCreate = allIslands.map(island => ({
                        name: islandRoleNames[island] || `${island} Ping`,
                        key: island,
                        type: 'island',
                        color: '#3498db'
                    }));
                    break;
            }

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Creating ${roleCategory}`)
                .setDescription('Please wait while I create the necessary roles...')
                .addFields([
                    { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '⚙️ Status', value: `🔄 Creating roles... (0/${rolesToCreate.length})`, inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            const createdRoles = [];
            const totalRoles = rolesToCreate.length;
            let roleCount = 0;

            for (const roleInfo of rolesToCreate) {
                try {
                    roleCount++;

                    // Update progress
                    progressEmbed.setFields(
                        { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                        { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})\nCurrent: **${roleInfo.name}**`, inline: true }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });

                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: `Created by setup command for ${roleCategory.toLowerCase()}`
                    });

                    createdRoles.push({ name: roleInfo.name, id: role.id });

                    if (roleInfo.type === 'world') {
                        currentRoles.worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        currentRoles.islandRoles[roleInfo.key] = role.id;
                    } else {
                        currentRoles.dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update progress to saving configuration
            progressEmbed.setFields(
                { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
            );
            await interaction.editReply({ embeds: [progressEmbed] });

            // Save configuration to MongoDB
            config.dungeonAlert.dungeonRoles = currentRoles.dungeonRoles;
            config.dungeonAlert.worldRoles = currentRoles.worldRoles;
            config.dungeonAlert.islandRoles = currentRoles.islandRoles;
            await configService.saveServerConfig(interaction.guild.id, config);

            // Update session with created roles
            this.updateSession(interaction.user.id, currentRoles);

            // Create success embed
            const successEmbed = new EmbedBuilder()
                .setTitle(`✅ ${roleCategory} Setup Complete!`)
                .setDescription(`${roleCategory} have been successfully created and configured for this server.\n\n**🚀 Configuration is now active!** The server will immediately start using these roles for dungeon alerts.`)
                .setColor('#2ecc71')
                .addFields(
                    { name: '📍 Alert Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '🎭 Roles Created', value: `${createdRoles.length} roles`, inline: true },
                    { name: '🔔 Status', value: 'Active & Ready', inline: true },
                    {
                        name: 'Created Roles',
                        value: createdRoles.map(role => `<@&${role.id}>`).join(', ') || 'None',
                        inline: false
                    }
                )
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [successEmbed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating specific roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },



    // Role validation and management methods
    async validateRoleExists(guild, roleId) {
        try {
            if (!guild || !roleId) return false;
            
            // Ensure roleId is a string
            const id = typeof roleId === 'string' ? roleId : String(roleId);
            
            // Check cache first
            const role = guild.roles.cache.get(id);
            if (role) return true;
            
            // Try to fetch from API if not in cache
            try {
                const fetchedRole = await guild.roles.fetch(id);
                return !!fetchedRole;
            } catch (fetchError) {
                return false;
            }
        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error validating role ${roleId}:`), error);
            return false;
        }
    },

    async validateRolesInDatabase(guild) {
        try {
            const config = await configService.getServerConfig(guild.id);
            if (!config?.dungeonAlert) return { valid: [], invalid: [] };

            const allRoles = {
                ...config.dungeonAlert.dungeonRoles,
                ...config.dungeonAlert.worldRoles,
                ...config.dungeonAlert.islandRoles
            };

            const valid = [];
            const invalid = [];

            for (const [key, roleId] of Object.entries(allRoles)) {
                if (roleId && await this.validateRoleExists(guild, roleId)) {
                    valid.push({ key, roleId });
                } else if (roleId) {
                    invalid.push({ key, roleId });
                }
            }

            return { valid, invalid };
        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error validating roles in database:`), error);
            return { valid: [], invalid: [] };
        }
    },

    async showExistingRoleSelection(interaction, roleType) {
        try {
            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            const roleKeys = this.getRoleKeysForType(roleType);
            const currentRoles = this.getExistingRolesByType(config, roleType);

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ASSIGN} Assign Existing ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription(`Select which ${roleType} role you want to assign:`)
                .addFields([
                    {
                        name: 'Instructions',
                        value: '1. Choose a role from the dropdown below\n2. After selecting, provide the role ID or mention the role in chat\n3. The role will be validated and assigned',
                        inline: false
                    }
                ])
                .setTimestamp();

            // Show current status if any roles are configured
            if (currentRoles.length > 0) {
                const statusText = currentRoles.map(role => {
                    const isValid = interaction.guild.roles.cache.has(role.roleId);
                    return `**${this.getRoleKeyDisplayName(role.key, roleType)}**: ${isValid ? `${SETUP_EMOJIS.VALIDATE} <@&${role.roleId}>` : `${SETUP_EMOJIS.INVALID_ROLE} Invalid Role`}`;
                }).join('\n');

                embed.addFields([
                    {
                        name: 'Current Configuration',
                        value: statusText,
                        inline: false
                    }
                ]);
            }

            // Create dropdown with role options
            const options = roleKeys.map(key => {
                const currentRole = currentRoles.find(r => r.key === key);
                const isConfigured = currentRole && interaction.guild.roles.cache.has(currentRole.roleId);

                return {
                    label: this.getRoleKeyDisplayName(key, roleType),
                    value: `assign_${roleType}_${key}`,
                    description: isConfigured ? 'Currently configured - select to update' : 'Not configured',
                    emoji: isConfigured ? SETUP_EMOJIS.SUCCESS : SETUP_EMOJIS.ERROR
                };
            });

            if (options.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No role options found for ${roleType}. This might be a configuration error.`,
                    embeds: [],
                    components: []
                });
            }

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_role_to_assign')
                .setPlaceholder('Choose a role to assign...')
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, {
                step: `${roleType}_role_assignment`,
                currentRoleType: roleType
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing existing role selection:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    getExistingRolesByType(config, roleType) {
        if (!config?.dungeonAlert) return [];

        switch (roleType) {
            case 'rank':
                return Object.entries(config.dungeonAlert.dungeonRoles || {})
                    .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS'].includes(key))
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'world':
                return Object.entries(config.dungeonAlert.worldRoles || {})
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'island':
                return Object.entries(config.dungeonAlert.islandRoles || {})
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'special':
                return Object.entries(config.dungeonAlert.dungeonRoles || {})
                    .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                    .map(([key, roleId]) => ({ key, roleId }));
            default:
                return [];
        }
    },

    getRoleTypeDisplayName(roleType) {
        const names = {
            rank: 'Rank Roles',
            world: 'World Roles',
            island: 'Island Roles',
            special: 'Special Roles'
        };
        return names[roleType] || 'Roles';
    },

    getMaxRolesForType(roleType) {
        switch (roleType) {
            case 'rank': return 7; // E, D, C, B, A, S, SS
            case 'world': return 2; // World 1, World 2
            case 'special': return 3; // DUNGEON_PING, RED_DUNGEON, DOUBLE_DUNGEON
            case 'island': return 12; // All islands
            default: return 1;
        }
    },

    async handleRoleSelection(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session?.currentRoleType) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    embeds: [],
                    components: []
                });
            }

            const selectedRoleIds = interaction.values;
            const roleType = session.currentRoleType;
            const guild = interaction.guild;

            // Validate selected roles exist
            const validRoles = [];
            for (const roleId of selectedRoleIds) {
                const role = guild.roles.cache.get(roleId);
                if (role) {
                    validRoles.push(role);
                }
            }

            if (validRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} None of the selected roles were found. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Show role mapping interface
            await this.showRoleMapping(interaction, validRoles, roleType);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role selection:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleMapping(interaction, selectedRoles, roleType) {
        try {
            const roleKeys = this.getRoleKeysForType(roleType);

            if (selectedRoles.length === 1 && roleKeys.length === 1) {
                // Direct mapping for single role types
                await this.assignRoleMapping(interaction, { [roleKeys[0]]: selectedRoles[0].id }, roleType);
                return;
            }

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.MAP} Map ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription('Assign the selected roles to specific functions:')
                .addFields([
                    {
                        name: 'Selected Roles',
                        value: selectedRoles.map(role => `<@&${role.id}>`).join(', '),
                        inline: false
                    },
                    {
                        name: 'Available Functions',
                        value: roleKeys.map(key => `**${key}**: ${this.getRoleKeyDescription(key, roleType)}`).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            // Create select menus for each role key
            const components = [];
            for (let i = 0; i < roleKeys.length; i += 5) { // Max 5 per row
                const chunk = roleKeys.slice(i, i + 5);
                const row = new ActionRowBuilder();

                for (const key of chunk) {
                    const selectMenu = new StringSelectMenuBuilder()
                        .setCustomId(`map_role_${roleType}_${key}`)
                        .setPlaceholder(`Select role for ${key}`)
                        .addOptions([
                            { label: 'None', value: 'none', description: 'Skip this role assignment' },
                            ...selectedRoles.map(role => ({
                                label: role.name.length > 100 ? role.name.substring(0, 97) + '...' : role.name,
                                value: role.id,
                                description: `Members: ${role.members.size}`
                            }))
                        ]);

                    row.addComponents(selectMenu);
                    if (row.components.length >= 1) break; // One select menu per row for better UX
                }
                components.push(row);
            }

            // Add control buttons
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_role_mapping')
                        .setLabel(`${SETUP_EMOJIS.CONFIRM_MAPPING} Confirm Mapping`)
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            components.push(controlRow);

            await interaction.update({
                embeds: [embed],
                components: components
            });

            this.updateSession(interaction.user.id, {
                step: 'role_mapping',
                selectedRoles: selectedRoles.map(r => ({ id: r.id, name: r.name })),
                roleMapping: {},
                roleType
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role mapping:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    getRoleKeysForType(roleType) {
        switch (roleType) {
            case 'rank':
                return ['E', 'D', 'C', 'B', 'A', 'S', 'SS'];
            case 'world':
                return ['1', '2'];
            case 'special':
                return ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
            case 'island':
                return ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town',
                       'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City',
                       'XZ City', 'Kindama City', 'Hunters City', 'Nen City', 'Hurricane Town'];
            default:
                return [];
        }
    },

    getRoleKeyDescription(key, roleType) {
        switch (roleType) {
            case 'rank':
                return `${key} rank dungeon alerts`;
            case 'world':
                return `World ${key} dungeon alerts`;
            case 'special':
                if (key === 'DUNGEON_PING') return 'General dungeon alerts';
                if (key === 'RED_DUNGEON') return 'Red gate alerts';
                if (key === 'DOUBLE_DUNGEON') return 'Double dungeon alerts';
                return key;
            case 'island':
                return `${key} island alerts`;
            default:
                return key;
        }
    },

    getRoleKeyDisplayName(key, roleType) {
        switch (roleType) {
            case 'rank':
                return `${key} Dungeon Ping`;
            case 'world':
                return `World ${key} Ping`;
            case 'special':
                if (key === 'DUNGEON_PING') return 'General Dungeon Ping';
                if (key === 'RED_DUNGEON') return 'Red Gate Ping';
                if (key === 'DOUBLE_DUNGEON') return 'Double Dungeon Ping';
                return key;
            case 'island':
                return `${key} Ping`;
            default:
                return key;
        }
    },

    async showRoleManagement(interaction) {
        try {
            const config = await configService.getServerConfig(interaction.guild.id);
            if (!config?.dungeonAlert) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No dungeon alert configuration found. Please set up the basic configuration first.`,
                    embeds: [],
                    components: []
                });
            }

            // Validate existing roles
            const { valid, invalid } = await this.validateRolesInDatabase(interaction.guild);

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ROLE_MANAGEMENT} Role Management`)
                .setDescription('Manage your existing ping role configuration:')
                .setTimestamp();

            if (valid.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.VALID_ROLES} Valid Roles`,
                        value: valid.map(role => `<@&${role.roleId}> (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (invalid.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.INVALID_ROLES} Invalid/Missing Roles`,
                        value: invalid.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (valid.length === 0 && invalid.length === 0) {
                embed.addFields([
                    {
                        name: 'No Roles Configured',
                        value: 'No ping roles are currently configured for this server.',
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('unset_roles')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_ROLES} Remove Roles`)
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(valid.length === 0 && invalid.length === 0),
                    new ButtonBuilder()
                        .setCustomId('cleanup_invalid_roles')
                        .setLabel(`${SETUP_EMOJIS.CLEAN_INVALID} Clean Invalid`)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(invalid.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

            this.updateSession(interaction.user.id, {
                step: 'role_management',
                validRoles: valid,
                invalidRoles: invalid
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role management:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleUnsetting(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const validRoles = session?.validRoles || [];
            const invalidRoles = session?.invalidRoles || [];

            if (validRoles.length === 0 && invalidRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No roles to remove.`,
                    embeds: [],
                    components: []
                });
            }

            const allRoles = [...validRoles, ...invalidRoles];
            const options = allRoles.map(role => ({
                label: role.key,
                value: `${role.key}:${role.roleId}`,
                description: validRoles.includes(role) ? 'Valid role' : 'Invalid/missing role'
            }));

            const embed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.REMOVE_ROLES} Remove Ping Roles`)
                .setDescription('Select which ping roles to remove from the bot configuration:')
                .addFields([
                    {
                        name: 'Note',
                        value: 'This will only remove the roles from the bot configuration. The actual Discord roles will remain unless you choose to delete them.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_roles_to_unset')
                .setPlaceholder('Choose roles to remove...')
                .setMinValues(1)
                .setMaxValues(Math.min(options.length, 25))
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('unset_all_roles')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_ALL} Remove All`)
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, { step: 'role_unsetting' });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role unsetting:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleUnsetting(interaction) {
        try {
            const isSelectMenu = interaction.isStringSelectMenu();
            const isButton = interaction.isButton();

            let rolesToRemove = [];

            if (isSelectMenu && interaction.customId === 'select_roles_to_unset') {
                rolesToRemove = interaction.values.map(value => {
                    const [key, roleId] = value.split(':');
                    return { key, roleId };
                });
            } else if (isButton && interaction.customId === 'unset_all_roles') {
                const session = this.getSession(interaction.user.id);
                rolesToRemove = [...(session?.validRoles || []), ...(session?.invalidRoles || [])];
            }

            if (rolesToRemove.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No roles selected for removal.`,
                    embeds: [],
                    components: []
                });
            }

            // Show confirmation with deletion options
            await this.showRoleRemovalConfirmation(interaction, rolesToRemove);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role unsetting:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleRemovalConfirmation(interaction, rolesToRemove) {
        try {
            const validRoles = [];
            const invalidRoles = [];

            // Separate valid and invalid roles
            for (const role of rolesToRemove) {
                if (await this.validateRoleExists(interaction.guild, role.roleId)) {
                    validRoles.push(role);
                } else {
                    invalidRoles.push(role);
                }
            }

            const embed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.CONFIRM_REMOVAL} Confirm Role Removal`)
                .setDescription('Choose how to handle the selected roles:')
                .setTimestamp();

            if (validRoles.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.VALID_ROLES} Valid Roles to Remove`,
                        value: validRoles.map(role => `<@&${role.roleId}> (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (invalidRoles.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.INVALID_ROLES} Invalid Roles to Clean`,
                        value: invalidRoles.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_remove_config_only')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_CONFIG_ONLY} Remove from Config Only`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('confirm_remove_and_delete')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_AND_DELETE} Remove & Delete Discord Roles`)
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(validRoles.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

            this.updateSession(interaction.user.id, {
                step: 'role_removal_confirmation',
                rolesToRemove,
                validRolesToRemove: validRoles,
                invalidRolesToRemove: invalidRoles
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role removal confirmation:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleDeletion(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const deleteDiscordRoles = interaction.customId === 'confirm_remove_and_delete';
            const rolesToRemove = session?.rolesToRemove || [];

            if (rolesToRemove.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No roles to remove.`,
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.PROCESSING} Processing role removal... Please wait.`,
                embeds: [],
                components: []
            });

            // Remove from database configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            if (config?.dungeonAlert) {
                for (const role of rolesToRemove) {
                    // Determine which role category to update
                    if (['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(role.key)) {
                        delete config.dungeonAlert.dungeonRoles[role.key];
                    } else if (['1', '2'].includes(role.key)) {
                        delete config.dungeonAlert.worldRoles[role.key];
                    } else {
                        delete config.dungeonAlert.islandRoles[role.key];
                    }
                }

                await configService.saveServerConfig(interaction.guild.id, config);
            }

            const results = {
                removedFromConfig: rolesToRemove.length,
                deletedFromDiscord: 0,
                failedDeletions: []
            };

            // Delete Discord roles if requested
            if (deleteDiscordRoles) {
                const validRoles = session?.validRolesToRemove || [];

                for (const role of validRoles) {
                    try {
                        const roleId = typeof role.roleId === 'string' ? role.roleId : String(role.roleId);
                        const discordRole = interaction.guild.roles.cache.get(roleId);
                        if (discordRole) {
                            await discordRole.delete('Removed via setup command');
                            results.deletedFromDiscord++;
                            console.log(chalk.green(`${SETUP_EMOJIS.SUCCESS} Deleted role: ${discordRole.name} (${roleId})`));
                        } else {
                            console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Role not found in cache: ${roleId} (${role.key})`));
                        }
                    } catch (error) {
                        console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to delete role ${role.key} (${role.roleId}):`), error);
                        results.failedDeletions.push(role.key);
                    }
                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }

            // Show completion message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.COMPLETE} Role Removal Complete`)
                .setDescription('Role removal has been completed successfully!')
                .addFields([
                    {
                        name: 'Summary',
                        value: [
                            `${SETUP_EMOJIS.REMOVE_CONFIG_ONLY} Removed from config: ${results.removedFromConfig}`,
                            deleteDiscordRoles ? `${SETUP_EMOJIS.DELETED_COUNT} Deleted from Discord: ${results.deletedFromDiscord}` : `${SETUP_EMOJIS.REMOVE_CONFIG_ONLY} Discord roles kept`,
                            results.failedDeletions.length > 0 ? `${SETUP_EMOJIS.FAILED_COUNT} Failed deletions: ${results.failedDeletions.join(', ')}` : ''
                        ].filter(Boolean).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role deletion:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async cleanupInvalidRoles(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const invalidRoles = session?.invalidRoles || [];

            if (invalidRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No invalid roles to clean up.`,
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.PROCESSING} Cleaning up invalid roles...`,
                embeds: [],
                components: []
            });

            // Remove invalid roles from database
            const config = await configService.getServerConfig(interaction.guild.id);
            if (config?.dungeonAlert) {
                for (const role of invalidRoles) {
                    if (['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(role.key)) {
                        delete config.dungeonAlert.dungeonRoles[role.key];
                    } else if (['1', '2'].includes(role.key)) {
                        delete config.dungeonAlert.worldRoles[role.key];
                    } else {
                        delete config.dungeonAlert.islandRoles[role.key];
                    }
                }

                await configService.saveServerConfig(interaction.guild.id, config);
            }

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.CLEANUP_COMPLETE} Cleanup Complete`)
                .setDescription(`Successfully cleaned up ${invalidRoles.length} invalid role(s) from the configuration.`)
                .addFields([
                    {
                        name: 'Cleaned Roles',
                        value: invalidRoles.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('manage_existing_roles')
                        .setLabel(`${SETUP_EMOJIS.MANAGE_ROLES} Manage Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error cleaning up invalid roles:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async confirmRoleMapping(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session?.roleMapping || !session?.roleType) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    embeds: [],
                    components: []
                });
            }

            const roleMapping = session.roleMapping;
            const roleType = session.roleType;

            // Validate mapping has at least one role assigned
            const assignedRoles = Object.values(roleMapping).filter(roleId => roleId && roleId !== 'none');
            if (assignedRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Please assign at least one role before confirming.`,
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.PROCESSING} Saving role configuration...`,
                embeds: [],
                components: []
            });

            // Save to session and immediately persist to MongoDB
            const currentRoles = {
                dungeonRoles: session.dungeonRoles || {},
                worldRoles: session.worldRoles || {},
                islandRoles: session.islandRoles || {}
            };

            // Update the appropriate role category
            for (const [key, roleId] of Object.entries(roleMapping)) {
                if (roleId && roleId !== 'none') {
                    if (roleType === 'world') {
                        currentRoles.worldRoles[key] = roleId;
                    } else if (roleType === 'island') {
                        currentRoles.islandRoles[key] = roleId;
                    } else {
                        currentRoles.dungeonRoles[key] = roleId;
                    }
                }
            }

            this.updateSession(interaction.user.id, currentRoles);

            // Persist to MongoDB immediately
            const config = await configService.getServerConfig(interaction.guild.id) || {
                serverId: interaction.guild.id,
                name: interaction.guild.name,
                dungeonAlert: {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                }
            };
            if (!config.dungeonAlert) config.dungeonAlert = { enabled: false, dungeonRoles: {}, worldRoles: {}, islandRoles: {} };
            config.dungeonAlert.dungeonRoles = currentRoles.dungeonRoles;
            config.dungeonAlert.worldRoles = currentRoles.worldRoles;
            config.dungeonAlert.islandRoles = currentRoles.islandRoles;
            await configService.saveServerConfig(interaction.guild.id, config);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} ${this.getRoleTypeDisplayName(roleType)} Configured`)
                .setDescription(`Successfully configured ${assignedRoles.length} ${roleType} role(s)!`)
                .addFields([
                    {
                        name: 'Configured Roles',
                        value: Object.entries(roleMapping)
                            .filter(([, roleId]) => roleId && roleId !== 'none')
                            .map(([key, roleId]) => `**${key}**: <@&${roleId}>`)
                            .join('\n') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error confirming role mapping:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleMapping(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} No active setup session found.`,
                    ephemeral: true
                });
            }

            // Parse the custom ID to get role key
            const parts = interaction.customId.split('_');
            if (parts.length < 4) return;

            const roleKey = parts.slice(3).join('_');
            const selectedRoleId = interaction.values[0];

            // Update session with the mapping
            if (!session.roleMapping) {
                session.roleMapping = {};
            }
            session.roleMapping[roleKey] = selectedRoleId;

            this.updateSession(interaction.user.id, session);

            // Acknowledge the selection
            await interaction.reply({
                content: `${SETUP_EMOJIS.SUCCESS} Mapped **${roleKey}** to ${selectedRoleId === 'none' ? 'None' : `<@&${selectedRoleId}>`}`,
                ephemeral: true
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role mapping:`), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async assignRoleMapping(interaction, roleMapping, roleType) {
        try {
            await interaction.update({
                content: `${SETUP_EMOJIS.PROCESSING} Saving role configuration...`,
                embeds: [],
                components: []
            });

            // Save to session for later database update
            const session = this.getSession(interaction.user.id);
            const currentRoles = {
                dungeonRoles: session?.dungeonRoles || {},
                worldRoles: session?.worldRoles || {},
                islandRoles: session?.islandRoles || {}
            };

            // Update the appropriate role category
            for (const [key, roleId] of Object.entries(roleMapping)) {
                if (roleId && roleId !== 'none') {
                    if (roleType === 'world') {
                        currentRoles.worldRoles[key] = roleId;
                    } else if (roleType === 'island') {
                        currentRoles.islandRoles[key] = roleId;
                    } else {
                        currentRoles.dungeonRoles[key] = roleId;
                    }
                }
            }

            this.updateSession(interaction.user.id, currentRoles);

            const assignedRoles = Object.values(roleMapping).filter(roleId => roleId && roleId !== 'none');

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} ${this.getRoleTypeDisplayName(roleType)} Configured`)
                .setDescription(`Successfully configured ${assignedRoles.length} ${roleType} role(s)!`)
                .addFields([
                    {
                        name: 'Configured Roles',
                        value: Object.entries(roleMapping)
                            .filter(([, roleId]) => roleId && roleId !== 'none')
                            .map(([key, roleId]) => `**${key}**: <@&${roleId}>`)
                            .join('\n') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error assigning role mapping:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleChannelSelection(interaction) {
        try {
            const selectedChannelId = interaction.values[0];
            const channel = interaction.guild.channels.cache.get(selectedChannelId);

            if (!channel) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.CHANNEL_NOT_FOUND} Selected channel not found. Please try again.`,
                    ephemeral: true
                });
            }

            // Save channel to database immediately
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        targetChannelId: selectedChannelId,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    }
                };
            } else {
                if (!config.dungeonAlert) {
                    config.dungeonAlert = {
                        enabled: false,
                        targetChannelId: selectedChannelId,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    };
                } else {
                    config.dungeonAlert.targetChannelId = selectedChannelId;
                }
            }
            await configService.saveServerConfig(interaction.guild.id, config);

            // Update session
            this.updateSession(interaction.user.id, {
                selectedChannelId: selectedChannelId,
                step: 'channel_selected'
            });

            // Show success message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.CHANNEL_SELECTED} Channel Selected & Saved`)
                .setDescription(`Successfully selected and saved ${channel} for dungeon alerts!`)
                .addFields([
                    {
                        name: 'Configuration Status',
                        value: `${SETUP_EMOJIS.SUCCESS} Channel has been automatically saved to the database`,
                        inline: false
                    },
                    {
                        name: 'Next Steps',
                        value: 'You can now set up ping roles or go back to the main setup.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.SETUP_PING_ROLES} Setup Ping Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK_TO_MAIN} Back to Main Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling channel selection:`), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async handleRoleAssignmentSelection(interaction) {
        try {
            const selectedValue = interaction.values[0];
            const parts = selectedValue.split('_');

            if (parts.length < 3 || parts[0] !== 'assign') {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.INVALID_FORMAT} Invalid selection. Please try again.`,
                    ephemeral: true
                });
            }

            const roleType = parts[1];
            const roleKey = parts.slice(2).join('_');
            const displayName = this.getRoleKeyDisplayName(roleKey, roleType);

            // Update session to track what we're assigning
            this.updateSession(interaction.user.id, {
                pendingRoleAssignment: {
                    roleType,
                    roleKey,
                    displayName,
                    channelId: interaction.channel.id,
                    userId: interaction.user.id
                }
            });

            // Create a collector to wait for the user's role input
            const filter = (message) => message.author.id === interaction.user.id;
            const collector = interaction.channel.createMessageCollector({
                filter,
                time: 60000, // 1 minute timeout
                max: 1
            });

            await interaction.reply({
                content: `${SETUP_EMOJIS.ROLE_INPUT} **Assigning ${displayName}**\n\nPlease provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*`,
                ephemeral: true
            });

            collector.on('collect', async (message) => {
                try {
                    await this.processRoleInput(message, roleType, roleKey);
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing role input:`), error);
                    await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
                }
            });

            collector.on('end', (collected) => {
                if (collected.size === 0) {
                    interaction.followUp({
                        content: `${SETUP_EMOJIS.TIMEOUT_WARNING} Role assignment timed out. Please try again.`,
                        ephemeral: true
                    }).catch(console.error);
                }
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role assignment selection:`), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async processRoleInput(message, roleType, roleKey) {
        try {
            const roleInput = message.content.trim();
            let roleId = null;

            // Parse role mention or ID
            const mentionMatch = roleInput.match(/^<@&(\d+)>$/);
            if (mentionMatch) {
                roleId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(roleInput)) {
                roleId = roleInput;
            }
            else {
                return await message.reply(`${SETUP_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a role mention (@RoleName) or a numeric role ID.`);
            }

            // Validate the role exists
            const role = message.guild.roles.cache.get(roleId);
            if (!role) {
                return await message.reply(`${SETUP_EMOJIS.ROLE_NOT_FOUND} Role not found. Please make sure the role exists in this server and try again.`);
            }

            // Save the role assignment
            await this.saveRoleAssignment(message, roleType, roleKey, roleId);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing role input:`), error);
            await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
        }
    },

    async saveRoleAssignment(message, roleType, roleKey, roleId) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }

            // Get current configuration
            const config = await configService.getServerConfig(message.guild.id) || {
                serverId: message.guild.id,
                name: message.guild.name,
                dungeonAlert: {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                }
            };

            // Update the appropriate role category
            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            if (roleType === 'world') {
                config.dungeonAlert.worldRoles[roleKey] = roleId;
            } else if (roleType === 'island') {
                config.dungeonAlert.islandRoles[roleKey] = roleId;
            } else {
                // rank and special roles go in dungeonRoles
                config.dungeonAlert.dungeonRoles[roleKey] = roleId;
            }

            // Save to database
            await configService.saveServerConfig(message.guild.id, config);

            // Update session with the new role
            const session = this.getSession(message.author.id);
            if (session) {
                if (!session.dungeonRoles) session.dungeonRoles = {};
                if (!session.worldRoles) session.worldRoles = {};
                if (!session.islandRoles) session.islandRoles = {};

                if (roleType === 'world') {
                    session.worldRoles[roleKey] = roleId;
                } else if (roleType === 'island') {
                    session.islandRoles[roleKey] = roleId;
                } else {
                    session.dungeonRoles[roleKey] = roleId;
                }

                this.updateSession(message.author.id, session);
            }

            // Find the original setup message and update it immediately
            const channel = message.channel;
            const lastMessages = await channel.messages.fetch({ limit: 10 });

            // Look for the setup message to update
            for (const [, msg] of lastMessages) {
                if (msg.author.id === message.client.user.id &&
                    msg.embeds.length > 0 &&
                    msg.embeds[0].title?.includes('Assign Existing')) {

                    // Create a mock interaction to refresh the interface
                    const mockInteraction = {
                        guild: message.guild,
                        user: message.author,
                        update: async (options) => {
                            await msg.edit(options);
                        }
                    };

                    await this.showExistingRoleSelection(mockInteraction, roleType);
                    break;
                }
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error saving role assignment:`), error);
            await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while saving the role assignment. Please try again.`);
        }
    },

    async processChannelInput(message) {
        try {
            const channelInput = message.content.trim();
            let channelId = null;

            // Parse channel mention or ID
            const mentionMatch = channelInput.match(/^<#(\d+)>$/);
            if (mentionMatch) {
                channelId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(channelInput)) {
                channelId = channelInput;
            }
            else {
                return await message.reply(`${SETUP_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a channel mention (#channel-name) or a numeric channel ID.`);
            }

            // Validate the channel exists and is a text channel
            const channel = message.guild.channels.cache.get(channelId);
            if (!channel) {
                return await message.reply(`${SETUP_EMOJIS.CHANNEL_NOT_FOUND} Channel not found. Please make sure the channel exists in this server and try again.`);
            }

            if (channel.type !== ChannelType.GuildText) {
                return await message.reply(`${SETUP_EMOJIS.INVALID_CHANNEL_TYPE} Invalid channel type. Please provide a text channel for dungeon alerts.`);
            }

            // Save the channel assignment
            await this.saveChannelAssignment(message, channelId, channel.name);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing channel input:`), error);
            await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
        }
    },

    async saveChannelAssignment(message, channelId, channelName) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }

            // Update session with selected channel
            this.updateSession(message.author.id, {
                selectedChannelId: channelId,
                step: 'channel_selected'
            });

            // Find the original setup message and update it immediately
            const channel = message.channel;
            const lastMessages = await channel.messages.fetch({ limit: 10 });

            // Look for the setup message to update
            for (const [, msg] of lastMessages) {
                if (msg.author.id === message.client.user.id &&
                    msg.embeds.length > 0 &&
                    msg.embeds[0].title?.includes('Select Existing Channel')) {

                    // Create success embed
                    const embed = new EmbedBuilder()
                        .setColor('#00ff00')
                        .setTitle(`${SETUP_EMOJIS.CHANNEL_SELECTED} Channel Selected`)
                        .setDescription(`Selected <#${channelId}> (${channelName}) for dungeon alerts!`)
                        .addFields([
                            {
                                name: 'Next Step',
                                value: 'Now you can set up ping roles or finish the setup.',
                                inline: false
                            }
                        ])
                        .setTimestamp();

                    const row = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId('dungeons_setup_roles')
                                .setLabel(`${SETUP_EMOJIS.SETUP_PING_ROLES} Setup Ping Roles`)
                                .setStyle(ButtonStyle.Primary),
                            new ButtonBuilder()
                                .setCustomId('dungeons_finish_setup')
                                .setLabel(`${SETUP_EMOJIS.FINISH_SETUP} Finish Setup`)
                                .setStyle(ButtonStyle.Success),
                            new ButtonBuilder()
                                .setCustomId('setup_back')
                                .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                                .setStyle(ButtonStyle.Secondary)
                        );

                    await msg.edit({ embeds: [embed], components: [row] });
                    break;
                }
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error saving channel assignment:`), error);
            await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while saving the channel assignment. Please try again.`);
        }
    },

async toggleRoleCategory(interaction, roleType) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {},
                        rankRolesEnabled: true,
                        worldRolesEnabled: true,
                        islandRolesEnabled: true,
                        specialRolesEnabled: true
                    },
                    worldBossAlert: { enabled: false },
                    infernalAlert: { enabled: false }
                };
            }

            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {},
                    rankRolesEnabled: true,
                    worldRolesEnabled: true,
                    islandRolesEnabled: true,
                    specialRolesEnabled: true
                };
            }

            // Get current state and toggle it
            const enabledKey = `${roleType}RolesEnabled`;
            const currentState = config.dungeonAlert[enabledKey] !== false; // Default to true
            const newState = !currentState;

            // If disabling and there are existing roles, show confirmation dialog
            if (!newState) {
                const hasRoles = this.hasConfiguredRoles(config, roleType);
                if (hasRoles) {
                    return await this.showDisableRoleConfirmation(interaction, roleType);
                }
            }

            // Update the state
            config.dungeonAlert[enabledKey] = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Refresh the appropriate setup page
            switch (roleType) {
                case 'rank':
                    await this.setupRankRoles(interaction);
                    break;
                case 'world':
                    await this.setupWorldRoles(interaction);
                    break;
                case 'island':
                    await this.setupIslandRoles(interaction);
                    break;
                case 'special':
                    await this.setupSpecialRoles(interaction);
                    break;
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling ${roleType} roles:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle ${roleType} roles. Please try again.`,
                ephemeral: true
            });
        }
    },

    hasConfiguredRoles(config, roleType) {
        switch (roleType) {
            case 'rank':
                const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS'];
                return rankKeys.some(key => config.dungeonAlert?.dungeonRoles?.[key]);
            case 'world':
                const worldKeys = ['1', '2'];
                return worldKeys.some(key => config.dungeonAlert?.worldRoles?.[key]);
            case 'island':
                return Object.keys(config.dungeonAlert?.islandRoles || {}).length > 0;
            case 'special':
                const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
                return specialKeys.some(key => config.dungeonAlert?.dungeonRoles?.[key]);
            default:
                return false;
        }
    },

    async showDisableRoleConfirmation(interaction, roleType) {
        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle(`${SETUP_EMOJIS.DISABLE_WARNING} Disable ${this.getRoleTypeDisplayName(roleType)}?`)
            .setDescription(`You have existing ${roleType} roles configured. What would you like to do with them?`)
            .addFields([
                {
                    name: 'Options',
                    value: '• **Keep Roles**: Disable pinging but keep role configurations\n• **Delete Roles**: Remove roles from Discord and database',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`disable_${roleType}_keep`)
                    .setLabel(`${SETUP_EMOJIS.KEEP_ROLES} Keep Roles (Disable Only)`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`disable_${roleType}_delete`)
                    .setLabel(`${SETUP_EMOJIS.DELETE_ROLES} Delete Roles`)
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, {
            step: 'disable_role_confirmation',
            roleType: roleType
        });
    },

    async handleDisableRoleKeep(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const roleType = session?.roleType;

            if (!roleType) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    ephemeral: true
                });
            }

            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            
            // Disable the role category but keep the roles
            const enabledKey = `${roleType}RolesEnabled`;
            config.dungeonAlert[enabledKey] = false;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Refresh the appropriate setup page
            switch (roleType) {
                case 'rank':
                    await this.setupRankRoles(interaction);
                    break;
                case 'world':
                    await this.setupWorldRoles(interaction);
                    break;
                case 'island':
                    await this.setupIslandRoles(interaction);
                    break;
                case 'special':
                    await this.setupSpecialRoles(interaction);
                    break;
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error disabling role category:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to disable role category. Please try again.`,
                ephemeral: true
            });
        }
    },

    async handleDisableRoleDelete(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const roleType = session?.roleType;

            if (!roleType) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    ephemeral: true
                });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.PROCESSING} Disabling and deleting roles... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            
            if (!config || !config.dungeonAlert) {
                return await interaction.editReply({
                    content: `${SETUP_EMOJIS.ERROR} No configuration found. Please set up the basic configuration first.`,
                    embeds: [],
                    components: []
                });
            }

            // Get roles to delete
            const rolesToDelete = this.getRolesToDelete(config, roleType);

            // Debug logging
            console.log(chalk.blue(`🔍 Debug - Role type: ${roleType}`));
            console.log(chalk.blue(`🔍 Debug - Roles to delete:`, rolesToDelete));
            console.log(chalk.blue(`🔍 Debug - Roles to delete length: ${rolesToDelete.length}`));
            console.log(chalk.blue(`🔍 Debug - Roles to delete types:`, rolesToDelete.map(r => typeof r)));

            // Additional safety check - filter out any non-string values
            const validRoleIds = rolesToDelete.filter(roleId => {
                const isValid = typeof roleId === 'string' && roleId.length > 0 && roleId !== 'null' && roleId !== 'undefined';
                if (!isValid) {
                    console.log(chalk.red(`🚫 Filtering out invalid role ID:`, roleId, `(type: ${typeof roleId})`));
                }
                return isValid;
            });

            console.log(chalk.blue(`🔍 Debug - Valid role IDs after filtering:`, validRoleIds));

            // Delete Discord roles
            let deletedCount = 0;
            let failedCount = 0;
            const failedRoles = [];

            for (const roleId of validRoleIds) {
                try {
                    // Ensure roleId is a string and not an object
                    const cleanRoleId = typeof roleId === 'string' ? roleId : String(roleId);

                    // Additional validation
                    if (!cleanRoleId || cleanRoleId === 'null' || cleanRoleId === 'undefined') {
                        console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Skipping invalid role ID: ${cleanRoleId}`));
                        continue;
                    }

                    const role = interaction.guild.roles.cache.get(cleanRoleId);
                    if (role) {
                        await role.delete('Disabled via setup command');
                        deletedCount++;
                        console.log(chalk.green(`${SETUP_EMOJIS.SUCCESS} Deleted role: ${role.name} (${cleanRoleId})`));
                    } else {
                        console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Role not found in cache: ${cleanRoleId}`));
                    }
                } catch (error) {
                    const cleanRoleId = typeof roleId === 'string' ? roleId : String(roleId);
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to delete role ${cleanRoleId}:`), error);
                    failedCount++;
                    failedRoles.push(cleanRoleId);
                }
                // Small delay to avoid rate limits
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Remove roles from database and disable category
            this.removeRolesFromConfig(config, roleType);
            const enabledKey = `${roleType}RolesEnabled`;
            config.dungeonAlert[enabledKey] = false;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show completion message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.DISABLED} ${this.getRoleTypeDisplayName(roleType)} Disabled`)
                .setDescription(`Successfully disabled ${roleType} roles and cleaned up Discord roles.`)
                .addFields([
                    {
                        name: 'Summary',
                        value: [
                            `${SETUP_EMOJIS.DELETED_COUNT} Deleted: ${deletedCount} roles`,
                            failedCount > 0 ? `${SETUP_EMOJIS.FAILED_COUNT} Failed: ${failedCount} roles` : `${SETUP_EMOJIS.ALL_SUCCESS_DELETE} All roles deleted successfully`,
                            `${SETUP_EMOJIS.DISABLED_CONFIG} ${this.getRoleTypeDisplayName(roleType)} disabled in configuration`
                        ].join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            if (failedRoles.length > 0) {
                embed.addFields([
                    {
                        name: 'Failed Role IDs',
                        value: failedRoles.join(', '),
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK_TO_MAIN} Back to Main Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error deleting roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to delete roles. Error: ${error.message}`,
                embeds: [],
                components: []
            });
        }
    },

    getRolesToDelete(config, roleType) {
        const roleIds = [];

        console.log(chalk.blue(`🔍 Debug getRolesToDelete - roleType: ${roleType}`));
        console.log(chalk.blue(`🔍 Debug getRolesToDelete - config.dungeonAlert:`, config.dungeonAlert));

        switch (roleType) {
            case 'rank':
                const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
                rankKeys.forEach(key => {
                    const roleId = config.dungeonAlert?.dungeonRoles?.[key];
                    if (roleId && roleId !== null && roleId !== 'null') {
                        // Ensure roleId is a string and valid
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added rank role ${key}: ${id}`));
                        }
                    }
                });
                break;
            case 'world':
                const worldKeys = ['1', '2'];
                worldKeys.forEach(key => {
                    const roleId = config.dungeonAlert?.worldRoles?.[key];
                    if (roleId && roleId !== null && roleId !== 'null') {
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added world role ${key}: ${id}`));
                        }
                    }
                });
                break;
            case 'island':
                const islandRoles = config.dungeonAlert?.islandRoles || {};
                console.log(chalk.blue(`🔍 Debug island roles object:`, islandRoles));

                Object.entries(islandRoles).forEach(([islandName, roleId]) => {
                    console.log(chalk.blue(`🔍 Processing island ${islandName}: ${roleId} (type: ${typeof roleId})`));
                    if (roleId && roleId !== null && roleId !== 'null') {
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added island role ${islandName}: ${id}`));
                        }
                    }
                });
                break;
            case 'special':
                const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
                specialKeys.forEach(key => {
                    const roleId = config.dungeonAlert?.dungeonRoles?.[key];
                    if (roleId && roleId !== null && roleId !== 'null') {
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added special role ${key}: ${id}`));
                        }
                    }
                });
                break;
        }

        console.log(chalk.blue(`🔍 Final roleIds array:`, roleIds));
        return roleIds;
    },

    removeRolesFromConfig(config, roleType) {
        if (!config.dungeonAlert) return;
        
        switch (roleType) {
            case 'rank':
                const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
                rankKeys.forEach(key => {
                    if (config.dungeonAlert.dungeonRoles) {
                        delete config.dungeonAlert.dungeonRoles[key];
                    }
                });
                break;
            case 'world':
                config.dungeonAlert.worldRoles = {};
                break;
            case 'island':
                config.dungeonAlert.islandRoles = {};
                break;
            case 'special':
                const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
                specialKeys.forEach(key => {
                    if (config.dungeonAlert.dungeonRoles) {
                        delete config.dungeonAlert.dungeonRoles[key];
                    }
                });
                break;
        }
    }
    
};

setInterval(() => {
    const now = Date.now();
    for (const [userId, session] of setupSessions.entries()) {
        if (now - session.startTime > SESSION_TIMEOUT) {
            setupSessions.delete(userId);
        }
    }
}, 60000); 








